<template>
  <div class="zy-home">
    <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirect-to-search="true" redirect-url="/search"
      @search="handleSearch" />

    <div class="banner-container">
      <transition name="skeleton-fade" mode="out-in">
        <BannerSkeleton v-if="skeletonStates.banner" key="banner-skeleton" />
        <GoodsSwiper v-else-if="headerBannerList.length > 0" key="banner-content" :imageList="headerBannerList"
          mode="landscape" paginationType="fraction" :autoplay="true" :loop="true" @image-click="handleBannerClick" />
      </transition>
    </div>

    <div class="grid-menu-container">
      <transition name="skeleton-fade" mode="out-in">
        <GridMenuSkeleton v-if="skeletonStates.gridMenu" key="grid-skeleton" />
        <IconGrid v-else-if="gridMenuItems.length > 0" key="grid-content" display-mode="scroll" :items="gridMenuItems"
          :columns="5" :show-more="true" :max-items="10" @item-click="handleGridItemClick"
          @more-click="handleMoreClick" />
      </transition>
    </div>

    <div class="sub-banner-container">
      <transition name="skeleton-fade" mode="out-in">
        <SubBannerSkeleton v-if="skeletonStates.subBanner" key="sub-banner-skeleton" />
        <div v-else-if="subBanner.length > 0" key="sub-banner-content" class="sub-banner">
          <img @click="onActivityClick(subBanner[0], 'bottom')" :src="subBanner[0].imgUrl" alt="" class="sub-banner-img" />
        </div>
      </transition>
    </div>

    <div class="third-banner-container">
      <transition name="skeleton-fade" mode="out-in">
        <ThirdBannerSkeleton v-if="skeletonStates.thirdBanner" key="third-banner-skeleton" />
        <div v-else-if="thirdBanner.length > 0" key="third-banner-content" class="third-banner">
          <div v-if="thirdBanner[0]" @click="onActivityClick(thirdBanner[0], 'left')" class="left">
            <img :src="thirdBanner[0].imgUrl" alt="" class="third-banner-img" />
          </div>
          <div v-if="thirdBanner[1]" @click="onActivityClick(thirdBanner[1], 'right')" class="right">
            <img :src="thirdBanner[1].imgUrl" alt="" class="third-banner-img" />
          </div>
        </div>
      </transition>
    </div>

    <div class="horizontal-scroll-container"
      :style="{ backgroundImage: specialPoolIdImage ? `url(${specialPoolIdImage})` : 'none' }">
      <transition name="skeleton-fade" mode="out-in">
        <SpecialGoodsSkeleton v-if="skeletonStates.specialGoods" :skeleton-count="5" key="newer-skeleton" />
        <div v-else-if="specialGoodsList.length > 0" key="newer-content" class="special-goods-content">
          <!-- 热区部分 -->
          <div class="hot-zone" @click="handleHotZoneClick">
            <!-- 热区内容可以根据需要添加 -->
          </div>

          <div class="horizontal-scroll-wrapper">
            <div class="goods-item" v-for="item in specialGoodsList" :key="item.goodsId"
              @click="handleGoodsClick(item)">
              <ProductCardMini :goods-info="item" @click="handleGoodsClick(item)" />
            </div>
          </div>
        </div>
      </transition>
    </div>


    <div class="goods-header-container">
      <transition name="skeleton-fade" mode="out-in">
        <GoodsHeaderSkeleton v-if="skeletonStates.goodsHeader" key="goods-header-skeleton" />
        <GoodsHeader v-else :typeList="typeList" key="goods-header-content" @switchTabs="switchTabs" />
      </transition>
    </div>


    <van-list :loading="false" :finished="true" :immediate-check="false">
      <div class="waterfall-container">
        <transition name="waterfall-fade" mode="out-in">
          <WaterfallSkeleton v-if="skeletonStates.waterfall" :skeleton-count="6" key="waterfall-skeleton" />
          <Waterfall v-else-if="waterfallGoodsList.length > 0" ref="waterfallWaterfallRef" key="waterfall-waterfall"
            :list="waterfallGoodsList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="0"
            :animationDelay="0" :backgroundColor="'transparent'" :horizontalOrder="true" :lazyload="true"
            @afterRender="handleWaterfallAfterRender">
            <template #default="{ item }">
              <ProductCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
            </template>
          </Waterfall>
        </transition>
      </div>
      <transition name="fade-up">
        <div class="load-more-container"
          v-if="waterfallGoodsList.length > 0 && !waterfallFinished && waterfallButtonCanShow && waterfallRenderComplete && !skeletonStates.waterfall">
          <WoButton type="text" :disabled="waterfallLoading" @click="handleWaterfallLoadMore" class="load-more-button">
            {{ waterfallLoading ? '加载中...' : '加载更多' }}
          </WoButton>
        </div>
      </transition>
      <transition name="fade-up">
        <div class="no-more-text"
          v-if="waterfallGoodsList.length > 0 && waterfallFinished && !skeletonStates.waterfall">
          <span>没有更多了</span>
        </div>
      </transition>
    </van-list>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { debounce } from 'lodash-es'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import SearchHeader from '@components/Common/SearchHeader.vue'
import GoodsImageSwiper from '@/components/Common/GoodsImageSwiper.vue'
import IconGrid from '@components/Common/Home/IconGrid.vue'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import { fenToYuan } from '@utils/amount.js'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast } from 'vant'

// Environment configuration
const envConfig = {
  JSON_CONFIG_CODE: import.meta.env.VITE_JSON_CONFIG_CODE,
  JSON_ICON_CONFIG_CODE: import.meta.env.VITE_JSON_ICON_CONFIG_CODE
}
import BannerSkeleton from '@views/Home/components/Skeleton/BannerSkeleton.vue'
import GridMenuSkeleton from '@views/Home/components/Skeleton/GridMenuSkeleton.vue'
import SubBannerSkeleton from '@views/Home/components/Skeleton/SubBannerSkeleton.vue'
import ThirdBannerSkeleton from '@views/Home/components/Skeleton/ThirdBannerSkeleton.vue'
import GoodsHeaderSkeleton from '@views/Home/components/Skeleton/GoodsHeaderSkeleton.vue'
import WaterfallSkeleton from '@views/Home/components/Skeleton/WaterfallSkeleton.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { getDefaultBreakpoints } from '@/config/responsive.js'
import ProductCard from '@components/Common/Home/ProductCard.vue'
import { getPartionList } from '@/api/interface/goods'
import GoodsSwiper from "@components/Common/GoodsSwiper.vue";
import GoodsHeader from '@components/Common/Home/GoodsHeader.vue'
import SpecialGoodsSkeleton from '@views/Home/components/Skeleton/SpecialGoodsSkeleton.vue'
import ProductCardMini from '@components/Common/Home/ProductCardMini.vue'

const router = useRouter()
const searchKeyword = ref('')
const headerBannerList = ref([])
const subBanner = ref([])
const thirdBanner = ref([])
const backImgUrl = ref('')
const gridMenuItems = ref([])
// 商品列表与分页状态
const waterfallGoodsList = ref([])
const waterfallLoading = ref(false)
const waterfallFinished = ref(false)
const waterfallCurrentPage = ref(1)
const waterfallPageSize = ref(10)
const waterfallIsFirstLoadComplete = ref(false)
const waterfallButtonCanShow = ref(false)
const waterfallRenderComplete = ref(false)

const waterfallWaterfallRef = ref(null)
const skeletonStates = ref({
  banner: true,
  gridMenu: true,
  subBanner: true,
  thirdBanner: true,
  specialGoods: true,
  goodsHeader: true,
  waterfall: true
})

const moduleDataReady = ref({
  banner: false,
  gridMenu: false,
  subBanner: false,
  thirdBanner: false,
  specialGoods: false,
  goodsHeader: false,
  waterfall: false
})


const breakpoints = ref(getDefaultBreakpoints())
const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}
const handleGoodsClick = (goodsInfo) => {
  if (goodsInfo.goodsId) {
    router.push(`/goodsdetail/${goodsInfo.goodsId}`)
  }
}

const hideSkeletonInOrder = async () => {
  if (moduleDataReady.value.banner && skeletonStates.value.banner) {
    skeletonStates.value.banner = false
    await nextTick()
  }

  if (moduleDataReady.value.gridMenu && skeletonStates.value.gridMenu) {
    skeletonStates.value.gridMenu = false
    await nextTick()
  }

  if (moduleDataReady.value.subBanner && skeletonStates.value.subBanner) {
    skeletonStates.value.subBanner = false
    await nextTick()
  }

  if (moduleDataReady.value.thirdBanner && skeletonStates.value.thirdBanner) {
    skeletonStates.value.thirdBanner = false
    await nextTick()
  }

  if (moduleDataReady.value.specialGoods && skeletonStates.value.specialGoods) {
    skeletonStates.value.specialGoods = false
    await nextTick()
  }

  if (moduleDataReady.value.goodsHeader && skeletonStates.value.goodsHeader) {
    skeletonStates.value.goodsHeader = false
    await nextTick()
  }
}

const loadBannerConfigInfo = async () => {
  showLoadingToast()
  const [, json] = await getBannerInfo({ bizCode: getBizCode(), showPage: 1 })
  headerBannerList.value = channelFilterd(json.sort((a, b) => b.orderBy - a.orderBy) || []).map(item => ({
    type: 'image',
    url: item.imgUrl,
    alt: item.bannerChName,
    linkUrl: item.url,
  }))
  moduleDataReady.value.banner = true
  const [, json1] = await getBannerInfo({ bizCode: getBizCode(), showPage: 2 })
  subBanner.value = channelFilterd(json1 || [])
  moduleDataReady.value.subBanner = true
  const [, json2] = await getBannerInfo({ bizCode: getBizCode(), showPage: 3 })
  thirdBanner.value = channelFilterd(json2 || [])
  moduleDataReady.value.thirdBanner = true
  const [, json3] = await getBannerInfo({ bizCode: getBizCode(), showPage: 4 })
  const backImgList = json3 || []
  if (backImgList.length > 0) {
    specialPoolIdImage.value = backImgList[0].imgUrl
  }
  closeToast()
  await hideSkeletonInOrder()
}

const getIconList = async () => {
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),
    channel: curChannelBiz.get(),
    showPage: 1
  })

  if (!err) {
    if (json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData
    } else {
      gridMenuItems.value = []
    }
  }

  moduleDataReady.value.gridMenu = true
  await hideSkeletonInOrder()
}

const handleSearch = debounce(() => {
  // 搜索逻辑可以在这里实现
}, 300)

const handleBannerClick = ({ item }) => {
  if (item.linkUrl) {
    window.location.href = item.linkUrl
  }
}

const handleGridItemClick = ({ item }) => {
  if (item.url) {
    window.location.href = item.url
  }
}

const handleMoreClick = () => {
}

const handleHotZoneClick = () => {
  // 热区点击逻辑，可以根据需要跳转到特殊商品池页面
  console.log('热区被点击')
}

// 左右分栏、第三级栏目点击
const onActivityClick = (item, position) => {
  if (position === 'left') {
    if (item.url) {
      window.location.href = item.url
    }
  } else if (position === 'right') {
    if (item.url) {
      window.location.href = item.url
    }
  } else {
    if (item.url) {
      window.location.href = item.url
    }
  }
}

// 获取特殊商品池数据
const getSpecialGoodsList = async () => {
  if (!specialPoolIdSelected.value) return

  const params = {
    type: 'partion',
    id: specialPoolIdSelected.value,
    bizCode: getBizCode('GOODS'),
    page_no: 1,
    page_size: 20,
  }

  const [err, json] = await getGoodsList(params)
  if (!err && json) {
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))
    specialGoodsList.value = newItems
  }

  moduleDataReady.value.specialGoods = true
  await hideSkeletonInOrder()
}

// 获取限量好物
const getWaterfallList = async (id, sortType, sort, isLoadMore = true) => {
  if (waterfallLoading.value || waterfallFinished.value) return
  waterfallLoading.value = true

  // 如果不是加载更多，重置渲染完成状态
  if (!isLoadMore) {
    waterfallRenderComplete.value = false
  }
  const params = {
    type: 'partion',
    id,
    bizCode: getBizCode('GOODS'),
    page_no: waterfallCurrentPage.value,
    page_size: waterfallPageSize.value,
  }
  if (sortType) {
    params.price_sort = sortType
  }
  const [err, json] = await getGoodsList(params)
  if (!err) {
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      // sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))



    waterfallGoodsList.value = isLoadMore ? [...waterfallGoodsList.value, ...newItems] : newItems
    if (json.length === 0) {
      waterfallFinished.value = true
    } else {
      waterfallFinished.value = false
    }

    if (isLoadMore) {
      waterfallCurrentPage.value++
    } else {
      waterfallCurrentPage.value = 2
    }


  }
  waterfallLoading.value = false
  waterfallIsFirstLoadComplete.value = true
  moduleDataReady.value.waterfall = true
  waterfallButtonCanShow.value = true
  if (skeletonStates.value.waterfall) skeletonStates.value.waterfall = false
}

const handleWaterfallLoadMore = () => {
  getWaterfallList(goodsPoolIdSelected.value, '', '', true)
}

const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

const typeList = ref([])              // 分区列表
const goodsPoolIdSelected = ref('')   // 当前选中的商品池 ID
const specialGoodsList = ref([])
const specialPoolIdSelected = ref('')
const specialPoolIdImage = ref('')

const switchTabs = async (id) => {
  waterfallCurrentPage.value = 1
  waterfallFinished.value = false
  waterfallLoading.value = false
  waterfallButtonCanShow.value = false
  waterfallRenderComplete.value = false
  goodsPoolIdSelected.value = id
  waterfallGoodsList.value = []
  skeletonStates.value.waterfall = true
  moduleDataReady.value.waterfall = false
  await nextTick()
  getWaterfallList(id, '', '', false)
}

// 重置并加载商品池商品
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  // 重置列表状态
  waterfallGoodsList.value = []
  waterfallCurrentPage.value = 1
  waterfallFinished.value = false
  waterfallRenderComplete.value = false
  // 调用已有瀑布流接口
  getWaterfallList(id, sortType, '', false)
}

// 初始化页面：获取分区列表并默认加载第一个分区商品
const initPage = async () => {
  showLoadingToast()
  const [err1, json1] = await getPartionList({ bizCode: getBizCode('GOODS'), type: 2 })
  closeToast()
  if (err1) {
    closeToast()
    return
  }
  typeList.value = json1 ? json1.sort((a, b) => b.pos - a.pos) : []

  // 标记 goodsHeader 数据已准备完成
  moduleDataReady.value.goodsHeader = true
  await hideSkeletonInOrder()

  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]
    goodsPoolIdSelected.value = recommond.id
    changeGoodsPool(recommond.id, '', '', false)
  }

  showLoadingToast()
  const [err2, json2] = await getPartionList({ bizCode: getBizCode('GOODS'), type: 1 })
  closeToast()
  if (err2) {
    closeToast()
    return
  }
  specialPoolIdSelected.value = json2[0].id
  // 获取特殊商品池数据
  getSpecialGoodsList()
}

onMounted(() => {
  loadBannerConfigInfo()
  getIconList()
  initPage()           // 替换原先直接加载瀑布流的调用
})

onUnmounted(() => {
})
</script>

<style scoped lang="less">
.zy-home {
  width: 100vw;
  height: 100%;
  overflow: auto;
  background: @bg-color-gray;

  .banner-container {
    margin: @radius-8 @radius-12;
    border-radius: @radius-12;
    overflow: hidden;
  }

  .grid-menu-container {
    //background: @bg-color-white;
    border-radius: @radius-12;
    margin: @radius-8 @radius-12;
  }

  .sub-banner-container {
    margin: @radius-8 @radius-12 30px;
  }

  .sub-banner {
    position: relative;
    width: 100%;

    .sub-banner-img {
      display: block;
      width: 100%;
      border-radius: @radius-12;
    }
  }

  .third-banner-container {
    margin: @radius-8 @radius-12 30px;
  }

  .third-banner {
    display: flex;
    height: 210px;
    gap: @radius-8;

    .left {
      display: flex;
      width: 50%;
    }

    .right {
      display: flex;
      width: 50%;
    }

    .third-banner-img {
      width: 100%;
      height: 210px;
      border-radius: @radius-12;
      object-fit: cover;
    }
  }

  .goods-header-container {
    //background: @bg-color-white;
    margin: 10px 0;
  }

  .horizontal-scroll-container {
    position: relative;
    //min-height: 300px;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: @radius-12;
    margin: @radius-8 @radius-12;
    overflow: hidden;

    .special-goods-content {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .hot-zone {
      flex: 1;
      min-height: 40px;
      cursor: pointer;

      // 热区样式，可以根据需要添加更多样式
      &:hover {
        opacity: 0.9;
      }
    }

    .horizontal-scroll-wrapper {
      display: flex;
      gap: @radius-12;
      overflow-x: auto;
      padding: @radius-12;
      scroll-behavior: smooth;
      width: 100%;
      .no-scrollbar();

      .goods-item {
        flex: 0 0 130px;
        cursor: pointer;

        &:last-child {
          margin-right: @radius-12;
        }
      }
    }
  }

  .waterfall-container {
    position: relative;
    //min-height: 500px;
    padding: 0 10px;
    box-sizing: border-box;

    :deep(.vue-waterfall) {
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  .waterfall-container {
    position: relative;
    //min-height: 500px;

    :deep(.vue-waterfall) {
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  // 瀑布流过渡动画
  .waterfall-fade-enter-active,
  .waterfall-fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .waterfall-fade-enter-from,
  .waterfall-fade-leave-to {
    opacity: 0;
  }

  .waterfall-fade-enter-to,
  .waterfall-fade-leave-from {
    opacity: 1;
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
    color: @text-color-tertiary;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;

    .horizontal-scroll-container & {
      position: static;
      transform: none;
      padding: 60px 0;
    }
  }

  .load-more-container {
    display: flex;
    justify-content: center;
    align-items: center;

    .load-more-button {
      min-width: 120px;
      height: @button-height-36;
      font-size: @font-size-14;

      &.wo-button-text {
        background-color: transparent;
        border-radius: @radius-18;

        &:active {
          opacity: @opacity-07;
        }

        &.wo-button-disabled {
          opacity: 0.6;
          cursor: not-allowed;

          &:active {
            transform: none;
          }
        }
      }
    }
  }

  .no-more-text {
    padding: 20px 0 16px;
    text-align: center;

    span {
      font-size: @font-size-14;
      color: @text-color-tertiary;
    }
  }

  // 骨架屏过渡动画
  .skeleton-fade-enter-active,
  .skeleton-fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .skeleton-fade-enter-from,
  .skeleton-fade-leave-to {
    opacity: 0;
  }

  .skeleton-fade-enter-to,
  .skeleton-fade-leave-from {
    opacity: 1;
  }
}
</style>
