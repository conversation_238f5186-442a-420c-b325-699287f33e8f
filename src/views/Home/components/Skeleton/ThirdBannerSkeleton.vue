<template>
  <div class="third-banner-skeleton">
    <div class="skeleton-third-banner">
      <div class="skeleton-left">
        <div class="skeleton-image"></div>
      </div>
      <div class="skeleton-right">
        <div class="skeleton-image"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件不需要任何逻辑
</script>

<style scoped lang="less">
// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.third-banner-skeleton {
  // 与实际 third-banner 样式保持一致
  margin: 8px 12px 30px;

  .skeleton-third-banner {
    display: flex;
    height: 210px;
    gap: 8px;

    .skeleton-left,
    .skeleton-right {
      display: flex;
      width: 50%;
      background: #ffffff;
      border-radius: 12px;
      overflow: hidden;

      .skeleton-image {
        .skeleton-base();
        width: 100%;
        height: 100%;
        border-radius: 12px;
        background-color: #f8f9fa;
      }
    }
  }
}

// 移动端适配
@media (max-width: 375px) {
  .third-banner-skeleton {
    margin: 6px 10px 24px;
    
    .skeleton-third-banner {
      height: 180px; // 移动端稍微降低高度
      gap: 6px;
    }
  }
}
</style>
