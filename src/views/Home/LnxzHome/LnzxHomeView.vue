<template>
  <div class="lnzx-home">
    <div class="bg" />
    <div class="header">
      <!-- <div class="title"></div> -->
      <img class="title" src="./assets/lnzx.png" alt="联农智选">
      <div class="search-box" @click="onSearchClick">
        <input type="text" placeholder="请搜索商品信息" readonly>
      </div>
    </div>
    <Swiper class="swiper-box" v-if="swiperList && swiperList.length > 0" :data="swiperList" @slideClick="onSlideClick"
            @slideChange="onSwipeChange">
      <template v-slot:slide="slotProps">
        <img :src="slotProps.item.imgUrl" :alt="slotProps.item.imgUrl">
      </template>
      <template v-slot:pagination>
        <div class="custom-indicator">
          <span class="current">{{ curSwiperIndex + 1 }}</span>
          <span class="separator">/</span>
          <span class="total">{{ swiperList.length }}</span>
        </div>
      </template>
    </Swiper>

    <div class="icon-list">
      <div class="icon-item" v-for="item in iconList" :key="item.imgUrl" @click="onIconItemClick(item)">
        <img class="icon-img" v-lazy="item.imgUrl">
        <div class="icon-name">{{ item.chName }}</div>
      </div>
    </div>

    <div class="goods-list" v-if="typeList.length > 0">
      <!-- <GoodsList v-if="typeList[0] && goodsList[typeList[0].id]" :goodsList="goodsList[typeList[0].id]" :title="typeList[0].name"
        desc="更多好物等你来看" :icon="goodsListPic0" more="去逛逛"
        containerStyle="background-image: linear-gradient(180deg, #DDFFF5 0%, #FFFFFF 18%);"
        @goodsClick="onGoodsItemClick" @moreClick="onMoreClick" /> -->

      <GoodsList v-if="typeList[0] && goodsList[typeList[0].id]" :goodsList="goodsList[typeList[0].id]" :title="typeList[0].name"
                 :icon="goodsListPic1" containerStyle="background-image: linear-gradient(180deg, #F9E8E6 0%, #FFFFFF 18%);"
                 @goodsClick="onGoodsItemClick" />

      <GoodsList v-if="typeList[1] && goodsList[typeList[1].id]" :goodsList="goodsList[typeList[1].id]" :title="typeList[1].name"
                 :icon="goodsListPic2" containerStyle="background-image: linear-gradient(180deg, #E6EAF9 0%, #FFFFFF 18%);"
                 @goodsClick="onGoodsItemClick" />

      <GoodsList v-if="typeList[2] && goodsList[typeList[2].id]" :goodsList="goodsList[typeList[2].id]" :title="typeList[2].name"
                 :icon="goodsListPic3" containerStyle="background-image: linear-gradient(180deg, #DDFFF5 0%, #FFFFFF 18%);"
                 @goodsClick="onGoodsItemClick" />

      <GoodsList v-if="typeList[3] && goodsList[typeList[3].id]" :goodsList="goodsList[typeList[3].id]" :title="typeList[3].name"
                 :icon="goodsListPic4" containerStyle="background-image: linear-gradient(180deg, #F9E8E6 0%, #FFFFFF 18%);"
                 @goodsClick="onGoodsItemClick" />

      <GoodsList v-if="typeList[4] && goodsList[typeList[4].id]" :goodsList="goodsList[typeList[4].id]" :title="typeList[4].name"
                 :icon="goodsListPic5" containerStyle="background-image: linear-gradient(180deg, #E6EAF9 0%, #FFFFFF 18%);"
                 @goodsClick="onGoodsItemClick" />

      <GoodsList v-if="typeList[5] && goodsList[typeList[5].id]" :goodsList="goodsList[typeList[5].id]" :title="typeList[5].name"
                 :icon="goodsListPic6" containerStyle="background-image: linear-gradient(180deg, #DDFFF5 0%, #FFFFFF 18%);"
                 @goodsClick="onGoodsItemClick" />

      <GoodsList v-if="typeList[6] && goodsList[typeList[6].id]" :goodsList="goodsList[typeList[6].id]" :title="typeList[6].name"
                 :icon="goodsListPic7" containerStyle="background-image: linear-gradient(180deg, #F9E8E6 0%, #FFFFFF 18%);"
                 @goodsClick="onGoodsItemClick" />

      <GoodsList v-if="typeList[7] && goodsList[typeList[7].id]" :goodsList="goodsList[typeList[7].id]" :title="typeList[7].name"
                 :icon="goodsListPic8" containerStyle="background-image: linear-gradient(180deg, #E6EAF9 0%, #FFFFFF 18%);"
                 @goodsClick="onGoodsItemClick" />
    </div>

    <!-- <div class="copyright">联农智选商城由乡村振兴数字产业研究院运营</div> -->
    <div style="height: 50px;" />
  </div>
</template>

<script>
import { isUnicom, isWopay } from 'commonkit'
import { Swiper } from 'wo-e2'
import GoodsList from './components/GoodsList.vue'
import { getBannerInfo, getIconInfo } from '@/api/bannerIcon'
import { getGoodsList, getPartionList } from '@/api/goods'
import { getBizCode, curChannelBiz } from '@/utils/curEnv'
// import goodsListPic0 from './assets/goods-list-0.png'
import goodsListPic1 from './assets/goods-list-1.png'
import goodsListPic2 from './assets/goods-list-2.png'
import goodsListPic3 from './assets/goods-list-3.png'
import goodsListPic4 from './assets/goods-list-4.png'
import goodsListPic5 from './assets/goods-list-5.png'
import goodsListPic6 from './assets/goods-list-6.png'
import goodsListPic7 from './assets/goods-list-7.png'
import goodsListPic8 from './assets/goods-list-8.png'


// banner 过滤对应渠道的数据
const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}

export default {
  components: { Swiper, GoodsList },
  data() {
    return {
      // goodsListPic0,
      goodsListPic1,
      goodsListPic2,
      goodsListPic3,
      goodsListPic4,
      goodsListPic5,
      goodsListPic6,
      goodsListPic7,
      goodsListPic8,
      swiperList: [], // 轮播图列表
      curSwiperIndex: 0, // 当前轮播图索引
      iconList: [], // ICON图标列表
      typeList: [], // 分类列表，不展示，再获取商品列表时使用
      goodsList: {}, // 商品列表，key为分类id
    }
  },
  created() {
    this.getSwiperList()
    this.getIconList()
    this.getList()
  },
  methods: {
    // 获取轮播图列表
    async getSwiperList() {
      const [, json] = await getBannerInfo({ bizCode: getBizCode('GOODS'), showPage: 1 })
      this.swiperList = channelFilterd(json)
    },
    // 获取ICON图标列表
    async getIconList() {
      const [, json] = await getIconInfo({ bizCode: getBizCode('QUERY'), channel: curChannelBiz.get(), showPage: 9 })
      this.iconList = json.slice(0, 4)
    },
    // 获取分类列表
    async getList() {
      const [err, json] = await getPartionList({ bizCode: getBizCode('GOODS'), type: 2 })
      if (err) return
      this.typeList = json ? json.sort((a, b) => a.pos - b.pos) : []
      this.typeList.forEach(item => {
        this.getGoodsList(item.id)
      })
    },
    // 获取商品
    async getGoodsList(id) {
      const [err, json] = await getGoodsList({ type: 'partion', bizCode: getBizCode('GOODS'), id, page_no: 1, page_size: 10 })
      if (err) return
      this.$set(this.goodsList, id, json)
    },
    // 搜索框点击，跳转搜索页
    onSearchClick() {
      this.$router.push('/search')
    },
    // 轮播图点击
    onSlideClick(index, item) {
      if (item.url) {
        window.location.href = item.url
      }
    },
    // 轮播图切换
    onSwipeChange(index) {
      this.curSwiperIndex = index
    },
    // ICON图标点击
    onIconItemClick(item) {
      if (item.url) window.location.href = item.url
    },
    // 商品点击
    onGoodsItemClick(item) {
      this.$router.push(`/goodsdetail/${item.id}/${item.skuList[0].skuId}`)
    },
    // 更多点击
    // onMoreClick() {
    //   const timestamp = Date.parse(new Date())
    //   // TODO 测试和生产 ID 不同，需要写死
    //   const listId = window.location.hostname === 'epay.10010.com' ? '587' : '840'
    //   this.$router.push({
    //     path: `/goodslist/${listId}`,
    //     query: {
    //       ...this.$route.query,
    //       timestamp: timestamp
    //     }
    //   })
    // }
  }
}
</script>

<style lang='less' scoped>
.lnzx-home {
  position: relative;
  height: 100vh;
  background-color: #f5f8fa;

  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 165px;
    background-image: linear-gradient(180deg, #00C557 0%, #00C557 46%, rgba(0, 197, 87, 0.00) 100%);
  }

  .header {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 17px 14px 12px;

    .title {
      flex-shrink: 0;
      margin-right: 14px;
      color: #fff;
      width: 88px;
    }

    .search-box {
      flex-grow: 1;
      overflow: hidden;
      height: 37px;
      background: #fff;
      border-radius: 20px;
      font-size: 0;
      position: relative;

      input {
        padding: 0 17px;
        padding-right: 40px;
        width: 100%;
        height: 100%;
        font-size: 14px;

        &::-webkit-input-placeholder {
          color: #C5C5C5;
        }
      }

      &::after {
        content: "";
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23C5C5C5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E") center no-repeat;
        background-size: contain;
      }
    }
  }

  .swiper-box {
    position: relative;
    margin: 0 10px;
    z-index: 1;

    .custom-indicator {
      position: absolute;
      right: 12px;
      bottom: 4px;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 25px;
      height: 16px;
      line-height: 16px;
      font-size: 12px;
      font-weight: 500;
      transform: scale(0.8);

      &::before {
        content: "";
        position: absolute;
        left: -2px;
        width: 18px;
        height: 18px;
        background-color: #fff;
        border-radius: 50%;
        z-index: -1;
      }

      &::after {
        content: "";
        position: absolute;
        right: -2px;
        width: 18px;
        height: 18px;
        background-color: #fff;
        border-radius: 50%;
        z-index: -1;
      }

      .current {
        color: #333;
      }

      .separator {
        margin: 0 2px;
        color: #999;
      }

      .total {
        color: #999;
      }
    }
  }

  .icon-list {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    margin: 16px 0;
    padding: 0 10px;
    background-color: #f5f8fa;

    .icon-item {
      width: 25%;
      padding: 10px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .icon-img {
        width: 60px;
        height: 60px;
      }

      .icon-name {
        margin-top: 8px;
        font-size: 14px;
        color: #333;
        text-align: center;
        font-weight: 400;
      }

    }
  }

  .goods-list {
    padding-bottom: 10px;
    background-color: #f5f8fa;
  }

  // .copyright {
  //   margin: 12px 0 20px;
  //   text-align: center;
  //   font-size: 12px;
  //   color: #979797;
  //   font-weight: 400;
  //   transform: scale(0.8);
  //   transform-origin: center;
  // }
}
</style>
